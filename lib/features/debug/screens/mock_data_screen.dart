import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../homework/mock/mock_classes.dart';
import '../../homework/mock/mock_homeworks.dart';
import '../../homework/mock/upload_mock_data.dart';
import '../../homework/mock/test_mock_data.dart';

class MockDataManagementScreen extends ConsumerStatefulWidget {
  const MockDataManagementScreen({super.key});

  @override
  ConsumerState<MockDataManagementScreen> createState() =>
      _MockDataManagementScreenState();
}

class _MockDataManagementScreenState
    extends ConsumerState<MockDataManagementScreen> {
  bool _isLoading = false;
  String? _lastAction;
  String? _lastResult;

  Future<void> _executeAction(
    String actionName,
    Future<void> Function() action,
  ) async {
    setState(() {
      _isLoading = true;
      _lastAction = actionName;
      _lastResult = null;
    });

    try {
      await action();
      setState(() {
        _lastResult = '✅ $actionName completed successfully';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ $actionName failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Mock Data Management')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Status Section
          if (_lastAction != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Last Action Status', style: textTheme.titleMedium),
                    const SizedBox(height: 8),
                    if (_isLoading)
                      Row(
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          const SizedBox(width: 8),
                          Text('Executing: $_lastAction...'),
                        ],
                      )
                    else if (_lastResult != null)
                      Text(
                        _lastResult!,
                        style: TextStyle(
                          color: _lastResult!.startsWith('✅')
                              ? Colors.green
                              : Colors.red,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Data Overview Section
          _buildSection('Data Overview', [_buildDataOverviewCard(textTheme)]),
          const SizedBox(height: 24),

          // Upload Actions Section
          _buildSection('Upload Actions', [
            _buildActionButton(
              'Upload All Mock Data',
              'Upload classes, homework, and submissions to Firebase',
              Icons.cloud_upload,
              Colors.blue,
              () => _executeAction('Upload All Mock Data', uploadAllMockData),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Classes Only',
              'Upload classroom data to Firebase',
              Icons.school,
              Colors.green,
              () => _executeAction('Upload Classes', uploadMockClasses),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Homework Only',
              'Upload homework assignments to Firebase',
              Icons.assignment,
              Colors.orange,
              () => _executeAction('Upload Homework', uploadMockHomework),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Submissions Only',
              'Upload homework submissions to Firebase',
              Icons.file_upload,
              Colors.purple,
              () => _executeAction('Upload Submissions', uploadMockSubmissions),
            ),
          ]),
          const SizedBox(height: 24),

          // Testing Actions Section
          _buildSection('Testing & Validation', [
            _buildActionButton(
              'Test Data Coverage',
              'Verify mock data meets all requirements',
              Icons.verified,
              Colors.teal,
              () => _executeAction('Test Data Coverage', () async {
                testMockDataCoverage();
                testDataConsistency();
              }),
            ),
          ]),
          const SizedBox(height: 24),

          // Cleanup Actions Section
          _buildSection('Cleanup Actions', [
            _buildActionButton(
              'Clear All Mock Data',
              'Remove all mock data from Firebase (DESTRUCTIVE)',
              Icons.delete_forever,
              Colors.red,
              () => _showClearDataDialog(),
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.headlineSmall),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDataOverviewCard(TextTheme textTheme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current Mock Data Statistics', style: textTheme.titleMedium),
            const SizedBox(height: 12),
            _buildStatRow('Classes', '${mockClassesList.length}'),
            _buildStatRow('Homework Items', '${mockHomeworkList.length}'),
            _buildStatRow('Submissions', '${mockHomeworkSubmissions.length}'),
            const SizedBox(height: 8),
            Text(
              'Current User ID: XRTanMcAUWSMq3mrRvve2Y9IMP12',
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback? onPressed,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: _isLoading ? null : const Icon(Icons.arrow_forward_ios),
        onTap: _isLoading ? null : onPressed,
        enabled: !_isLoading,
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Mock Data'),
        content: const Text(
          'This will permanently delete all mock data from Firebase. '
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _executeAction('Clear All Mock Data', clearAllMockData);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
