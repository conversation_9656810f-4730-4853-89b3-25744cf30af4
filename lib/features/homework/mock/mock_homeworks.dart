import 'package:faker/faker.dart';
import 'package:uuid/uuid.dart';
import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';
import 'mock_classes.dart';

const _uuid = Uuid();

/// Mock data for homework assignments with comprehensive test coverage
final List<HomeworkModel> mockHomeworkList = _generateMockHomework();

/// Generate comprehensive mock homework data ensuring each status has at least 3 test cases
List<HomeworkModel> _generateMockHomework() {
  final faker = Faker();
  final homeworkList = <HomeworkModel>[];

  // Get available classes for assignment
  final availableClasses = mockClassesList;

  // Define subjects with realistic homework topics
  final subjectTopics = {
    'Mathematics': [
      'Algebra Practice Problems',
      'Geometry Proofs',
      'Calculus Integration',
      'Statistics Analysis',
      'Trigonometry Applications',
      'Linear Equations',
      'Probability Theory',
    ],
    'Physics': [
      'Newton\'s Laws Lab Report',
      'Electromagnetic Waves Study',
      'Thermodynamics Problems',
      'Quantum Mechanics Basics',
      'Optics Experiments',
      'Motion Analysis',
      'Energy Conservation',
    ],
    'Chemistry': [
      'Organic Compounds Research',
      'Chemical Reactions Lab',
      'Periodic Table Study',
      'Molecular Structure Analysis',
      'Acid-Base Titration',
      'Stoichiometry Problems',
      'Electrochemistry Report',
    ],
    'Biology': [
      'Cell Division Study',
      'Ecosystem Analysis',
      'Genetics Problems',
      'Human Anatomy Report',
      'Plant Biology Research',
      'Evolution Theory Essay',
      'Microbiology Lab',
    ],
    'English Literature': [
      'Shakespeare Analysis',
      'Poetry Interpretation',
      'Novel Character Study',
      'Creative Writing Assignment',
      'Literary Criticism Essay',
      'Grammar Exercises',
      'Reading Comprehension',
    ],
    'History': [
      'World War Analysis',
      'Ancient Civilizations Report',
      'Historical Timeline Project',
      'Primary Source Analysis',
      'Cultural Studies Essay',
      'Political Systems Comparison',
      'Archaeological Research',
    ],
    'Computer Science': [
      'Algorithm Implementation',
      'Data Structure Problems',
      'Web Development Project',
      'Database Design',
      'Software Testing',
      'Programming Exercises',
      'System Analysis',
    ],
  };

  // Ensure each homework status has at least 3 test cases
  final statusCounts = <HomeworkStatus, int>{
    HomeworkStatus.pending: 0,
    HomeworkStatus.done: 0,
    HomeworkStatus.submitted: 0,
    HomeworkStatus.accepted: 0,
    HomeworkStatus.rejected: 0,
  };

  // Generate homework ensuring status coverage
  for (int i = 0; i < 50; i++) {
    // Determine status ensuring minimum coverage
    HomeworkStatus status;
    if (i < 15) {
      // First 15 assignments ensure 3 of each status
      status = HomeworkStatus.values[i % 5];
    } else {
      // Remaining assignments can be random
      status = HomeworkStatus.values[faker.randomGenerator.integer(5)];
    }
    statusCounts[status] = (statusCounts[status] ?? 0) + 1;

    // Select random class and subject
    final selectedClass =
        availableClasses[faker.randomGenerator.integer(
          availableClasses.length,
        )];
    final subject = selectedClass.subject;
    final topics = subjectTopics[subject] ?? ['General Assignment'];
    final topic = topics[faker.randomGenerator.integer(topics.length)];

    // Generate realistic dates
    final assignedDaysAgo = faker.randomGenerator.integer(30, min: 1);
    final assignedAt = DateTime.now().subtract(Duration(days: assignedDaysAgo));

    // Due date logic based on status
    DateTime? dueAt;
    if (status == HomeworkStatus.pending || status == HomeworkStatus.rejected) {
      // Future due date for pending/rejected
      dueAt = assignedAt.add(
        Duration(days: faker.randomGenerator.integer(14, min: 3)),
      );
    } else {
      // Past due date for completed assignments
      dueAt = assignedAt.add(
        Duration(days: faker.randomGenerator.integer(7, min: 1)),
      );
    }

    // Determine submission requirements
    final requiresSubmission = status != HomeworkStatus.done;
    final submissionType = requiresSubmission
        ? (faker.randomGenerator.boolean()
              ? SubmissionType.online
              : SubmissionType.offline)
        : SubmissionType.offline;

    // Assignment type distribution
    final assignmentType = i < 35
        ? AssignmentType.classAssignment
        : (i < 45 ? AssignmentType.individual : AssignmentType.group);

    // Generate assigned user IDs for individual/group assignments
    final assignedUserIds = <String>[];
    if (assignmentType == AssignmentType.individual) {
      final userCount = faker.randomGenerator.integer(3, min: 1);
      assignedUserIds.addAll(selectedClass.studentIds.take(userCount).toList());
    } else if (assignmentType == AssignmentType.group) {
      final userCount = faker.randomGenerator.integer(6, min: 3);
      assignedUserIds.addAll(selectedClass.studentIds.take(userCount).toList());
    }

    homeworkList.add(
      HomeworkModel(
        id: _uuid.v4(),
        subject: subject,
        title: topic,
        description: faker.randomGenerator.boolean()
            ? faker.lorem
                  .sentences(faker.randomGenerator.integer(3, min: 1))
                  .join(' ')
            : null,
        assignedAt: assignedAt,
        dueAt: dueAt,
        requiresSubmission: requiresSubmission,
        submissionType: submissionType,
        status: status,
        resourceUrls: List.generate(
          faker.randomGenerator.integer(3, min: 0),
          (i) =>
              'https://example.com/resources/${faker.lorem.word()}_${i + 1}.pdf',
        ),
        teacherNote: faker.randomGenerator.boolean()
            ? faker.lorem.sentence()
            : null,
        classId: assignmentType == AssignmentType.classAssignment
            ? selectedClass.id
            : null,
        teacherId: selectedClass.teacherId,
        submissionId:
            (status == HomeworkStatus.submitted ||
                status == HomeworkStatus.accepted ||
                status == HomeworkStatus.rejected)
            ? _uuid.v4()
            : null,
        assignmentType: assignmentType,
        assignedUserIds: assignedUserIds,
      ),
    );
  }

  return homeworkList;
}

/// Mock data for homework submissions with comprehensive test coverage
final List<HomeworkSubmissionModel> mockHomeworkSubmissions =
    _generateMockSubmissions();

/// Generate comprehensive mock homework submission data
List<HomeworkSubmissionModel> _generateMockSubmissions() {
  final faker = Faker();
  final submissions = <HomeworkSubmissionModel>[];

  // Get homework that requires submissions
  final submittableHomework = mockHomeworkList
      .where(
        (hw) =>
            hw.status == HomeworkStatus.submitted ||
            hw.status == HomeworkStatus.accepted ||
            hw.status == HomeworkStatus.rejected,
      )
      .toList();

  // Generate submissions for each submittable homework
  for (final homework in submittableHomework) {
    final fileCount = faker.randomGenerator.integer(4, min: 1);
    final extensions = [
      'pdf',
      'docx',
      'jpg',
      'png',
      'txt',
      'py',
      'java',
      'cpp',
      'html',
      'css',
    ];

    // Generate realistic teacher remarks based on status
    String? teacherRemark;
    DateTime? reviewedAt;

    if (homework.status == HomeworkStatus.accepted) {
      final positiveRemarks = [
        'Excellent work! Your analysis is thorough and well-structured.',
        'Great job! You demonstrated a clear understanding of the concepts.',
        'Outstanding submission. Your approach is innovative and correct.',
        'Well done! Your work shows excellent attention to detail.',
        'Impressive work! You exceeded the requirements.',
      ];
      teacherRemark =
          positiveRemarks[faker.randomGenerator.integer(
            positiveRemarks.length,
          )];
      reviewedAt = DateTime.now().subtract(
        Duration(hours: faker.randomGenerator.integer(72, min: 1)),
      );
    } else if (homework.status == HomeworkStatus.rejected) {
      final negativeRemarks = [
        'Please revise your work. The analysis needs more depth and proper citations are missing.',
        'Your submission doesn\'t meet the requirements. Please review the guidelines and resubmit.',
        'The work is incomplete. Please add more detail and examples.',
        'Your approach is incorrect. Please review the material and try again.',
        'Missing key components. Please ensure all requirements are addressed.',
      ];
      teacherRemark =
          negativeRemarks[faker.randomGenerator.integer(
            negativeRemarks.length,
          )];
      reviewedAt = DateTime.now().subtract(
        Duration(hours: faker.randomGenerator.integer(48, min: 1)),
      );
    }

    submissions.add(
      HomeworkSubmissionModel(
        id: homework.submissionId!,
        homeworkId: homework.id,
        userId: faker.randomGenerator.boolean()
            ? 'student_001' // Current user
            : 'student_${faker.randomGenerator.integer(20, min: 2).toString().padLeft(3, '0')}',
        fileUrls: List.generate(fileCount, (i) {
          final ext =
              extensions[faker.randomGenerator.integer(extensions.length)];
          return 'https://example.com/submissions/${faker.lorem.word()}_${i + 1}.$ext';
        }),
        studentNote: faker.randomGenerator.boolean()
            ? faker.lorem.sentence()
            : null,
        submittedAt: homework.assignedAt.add(
          Duration(
            days: faker.randomGenerator.integer(7, min: 1),
            hours: faker.randomGenerator.integer(24),
          ),
        ),
        teacherRemark: teacherRemark,
        reviewedAt: reviewedAt,
      ),
    );
  }

  return submissions;
}
