import 'package:faker/faker.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/class_model.dart';

const _uuid = Uuid();

/// Mock data for classes with realistic data generation
final List<ClassModel> mockClassesList = _generateMockClasses();

/// Generate realistic mock classroom data
List<ClassModel> _generateMockClasses() {
  final faker = Faker();
  final classes = <ClassModel>[];

  // Define realistic subjects and grade levels
  final subjects = [
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English Literature',
    'History',
    'Geography',
    'Computer Science',
    'Art',
    'Music',
  ];

  final gradeLevels = ['10', '11', '12'];
  final sections = ['A', 'B', 'C'];

  // Generate teacher pool with realistic names
  final teachers = List.generate(15, (index) {
    final titles = ['Dr.', 'Prof.', 'Ms.', 'Mr.'];
    final title = titles[faker.randomGenerator.integer(titles.length)];
    return {
      'id': _uuid.v4(),
      'name': '$title ${faker.person.firstName()} ${faker.person.lastName()}',
    };
  });

  // Generate student pool with current user included
  final currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
  final students = [currentUserId, ...List.generate(49, (index) => _uuid.v4())];

  // Create 5 main classes with good coverage
  for (int i = 0; i < 5; i++) {
    final subject = subjects[i % subjects.length];
    final gradeLevel =
        gradeLevels[faker.randomGenerator.integer(gradeLevels.length)];
    final section = sections[faker.randomGenerator.integer(sections.length)];
    final teacher = teachers[i % teachers.length];

    // Generate realistic student enrollment (15-25 students per class)
    final studentCount = faker.randomGenerator.integer(25, min: 15);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: '$subject $gradeLevel$section',
        subject: subject,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(200, min: 30)),
        ),
        isActive: true,
      ),
    );
  }

  return classes;
}
